module.exports = {

"[project]/.next-internal/server/app/api/discord-auth/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/api/discord-auth/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
async function POST(request) {
    try {
        const { code } = await request.json();
        if (!code) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No code provided'
            }, {
                status: 400
            });
        }
        console.log('Attempting Discord OAuth with code:', code.substring(0, 10) + '...');
        // Try to exchange code for access token with retry mechanism
        let tokenData = null;
        let attempts = 0;
        const maxAttempts = 3;
        while(attempts < maxAttempts && !tokenData){
            try {
                attempts++;
                console.log(`Discord token exchange attempt ${attempts}/${maxAttempts}`);
                const tokenResponse = await fetch('https://discord.com/api/oauth2/token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: new URLSearchParams({
                        client_id: process.env.DISCORD_CLIENT_ID,
                        client_secret: process.env.DISCORD_CLIENT_SECRET,
                        grant_type: 'authorization_code',
                        code: code,
                        redirect_uri: 'http://localhost:3000'
                    }),
                    signal: AbortSignal.timeout(15000) // 15 second timeout
                });
                if (!tokenResponse.ok) {
                    const errorText = await tokenResponse.text();
                    console.error(`Token response error (${tokenResponse.status}):`, errorText);
                    if (attempts === maxAttempts) {
                        // If all attempts failed, return mock data as fallback
                        console.log('All Discord API attempts failed, using mock data');
                        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                            id: '123456789',
                            username: 'MockUser_' + Date.now().toString().slice(-4),
                            email: '<EMAIL>',
                            avatar: null,
                            isMock: true
                        });
                    }
                    continue;
                }
                tokenData = await tokenResponse.json();
                console.log('Token exchange successful');
                break;
            } catch (fetchError) {
                console.error(`Discord API attempt ${attempts} failed:`, fetchError.message);
                if (attempts === maxAttempts) {
                    // If all attempts failed, return mock data as fallback
                    console.log('All Discord API attempts failed, using mock data');
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                        id: '123456789',
                        username: 'MockUser_' + Date.now().toString().slice(-4),
                        email: '<EMAIL>',
                        avatar: null,
                        isMock: true
                    });
                }
                // Wait before retry
                await new Promise((resolve)=>setTimeout(resolve, 1000 * attempts));
            }
        }
        if (!tokenData?.access_token) {
            console.log('No access token received, using mock data');
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                id: '123456789',
                username: 'MockUser_' + Date.now().toString().slice(-4),
                email: '<EMAIL>',
                avatar: null,
                isMock: true
            });
        }
        // Get user info from Discord
        try {
            console.log('Fetching user info from Discord...');
            const userResponse = await fetch('https://discord.com/api/users/@me', {
                headers: {
                    Authorization: `Bearer ${tokenData.access_token}`
                },
                signal: AbortSignal.timeout(10000)
            });
            if (!userResponse.ok) {
                throw new Error(`User fetch failed: ${userResponse.status}`);
            }
            const userData = await userResponse.json();
            console.log('User data received:', userData.username);
            // Get user's guilds to check BackstabCO membership
            let userGuilds = [];
            let userRoles = [];
            try {
                console.log('Fetching user guilds...');
                const guildsResponse = await fetch('https://discord.com/api/users/@me/guilds', {
                    headers: {
                        Authorization: `Bearer ${tokenData.access_token}`
                    },
                    signal: AbortSignal.timeout(10000)
                });
                if (guildsResponse.ok) {
                    userGuilds = await guildsResponse.json();
                    console.log('User guilds fetched:', userGuilds.length);
                    // Check if user is in BackstabCO server
                    const backstabGuild = userGuilds.find((guild)=>guild.id === '1366161562238451853');
                    if (backstabGuild) {
                        console.log('User is in BackstabCO server');
                        // Try to get user roles in BackstabCO server
                        // Note: This requires bot token, so we'll simulate for now
                        userRoles = [
                            'Player'
                        ] // Default role
                        ;
                        // Check if user has admin permissions in the guild
                        if (backstabGuild.permissions && parseInt(backstabGuild.permissions) & 0x8) {
                            userRoles.push('Admin');
                        }
                    }
                }
            } catch (guildsError) {
                console.error('Failed to fetch guilds:', guildsError.message);
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                id: userData.id,
                username: userData.username,
                email: userData.email,
                avatar: userData.avatar,
                discriminator: userData.discriminator,
                guilds: userGuilds.length,
                roles: userRoles,
                isBackstabMember: userGuilds.some((guild)=>guild.id === '1366161562238451853'),
                isMock: false
            });
        } catch (userError) {
            console.error('Failed to fetch user data:', userError.message);
            // Return mock data as fallback
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                id: '123456789',
                username: 'MockUser_' + Date.now().toString().slice(-4),
                email: '<EMAIL>',
                avatar: null,
                isMock: true
            });
        }
    } catch (error) {
        console.error('Discord auth error:', error);
        // Return mock data as fallback
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            id: '123456789',
            username: 'MockUser_' + Date.now().toString().slice(-4),
            email: '<EMAIL>',
            avatar: null,
            isMock: true
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__c5ba58f1._.js.map