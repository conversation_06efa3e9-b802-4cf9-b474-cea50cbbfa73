{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/AI/AugmentCode/BackstabCO/backstab-events/src/app/api/discord-auth/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { code } = await request.json()\n\n    if (!code) {\n      return NextResponse.json({ error: 'No code provided' }, { status: 400 })\n    }\n\n    console.log('Attempting Discord OAuth with code:', code.substring(0, 10) + '...')\n\n    // Try to exchange code for access token with retry mechanism\n    let tokenData = null\n    let attempts = 0\n    const maxAttempts = 3\n\n    while (attempts < maxAttempts && !tokenData) {\n      try {\n        attempts++\n        console.log(`Discord token exchange attempt ${attempts}/${maxAttempts}`)\n\n        const tokenResponse = await fetch('https://discord.com/api/oauth2/token', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/x-www-form-urlencoded',\n          },\n          body: new URLSearchParams({\n            client_id: process.env.DISCORD_CLIENT_ID!,\n            client_secret: process.env.DISCORD_CLIENT_SECRET!,\n            grant_type: 'authorization_code',\n            code: code,\n            redirect_uri: 'http://localhost:3000',\n          }),\n          signal: AbortSignal.timeout(15000) // 15 second timeout\n        })\n\n        if (!tokenResponse.ok) {\n          const errorText = await tokenResponse.text()\n          console.error(`Token response error (${tokenResponse.status}):`, errorText)\n\n          if (attempts === maxAttempts) {\n            // If all attempts failed, return mock data as fallback\n            console.log('All Discord API attempts failed, using mock data')\n            return NextResponse.json({\n              id: '123456789',\n              username: 'MockUser_' + Date.now().toString().slice(-4),\n              email: '<EMAIL>',\n              avatar: null,\n              isMock: true\n            })\n          }\n          continue\n        }\n\n        tokenData = await tokenResponse.json()\n        console.log('Token exchange successful')\n        break\n\n      } catch (fetchError) {\n        console.error(`Discord API attempt ${attempts} failed:`, fetchError.message)\n\n        if (attempts === maxAttempts) {\n          // If all attempts failed, return mock data as fallback\n          console.log('All Discord API attempts failed, using mock data')\n          return NextResponse.json({\n            id: '123456789',\n            username: 'MockUser_' + Date.now().toString().slice(-4),\n            email: '<EMAIL>',\n            avatar: null,\n            isMock: true\n          })\n        }\n\n        // Wait before retry\n        await new Promise(resolve => setTimeout(resolve, 1000 * attempts))\n      }\n    }\n\n    if (!tokenData?.access_token) {\n      console.log('No access token received, using mock data')\n      return NextResponse.json({\n        id: '123456789',\n        username: 'MockUser_' + Date.now().toString().slice(-4),\n        email: '<EMAIL>',\n        avatar: null,\n        isMock: true\n      })\n    }\n\n    // Get user info from Discord\n    try {\n      console.log('Fetching user info from Discord...')\n      const userResponse = await fetch('https://discord.com/api/users/@me', {\n        headers: {\n          Authorization: `Bearer ${tokenData.access_token}`,\n        },\n        signal: AbortSignal.timeout(10000)\n      })\n\n      if (!userResponse.ok) {\n        throw new Error(`User fetch failed: ${userResponse.status}`)\n      }\n\n      const userData = await userResponse.json()\n      console.log('User data received:', userData.username)\n\n      // Get user's guilds to check BackstabCO membership\n      let userGuilds = []\n      let userRoles = []\n\n      try {\n        console.log('Fetching user guilds...')\n        const guildsResponse = await fetch('https://discord.com/api/users/@me/guilds', {\n          headers: {\n            Authorization: `Bearer ${tokenData.access_token}`,\n          },\n          signal: AbortSignal.timeout(10000)\n        })\n\n        if (guildsResponse.ok) {\n          userGuilds = await guildsResponse.json()\n          console.log('User guilds fetched:', userGuilds.length)\n\n          // Check if user is in BackstabCO server\n          const backstabGuild = userGuilds.find(guild => guild.id === '1366161562238451853')\n          if (backstabGuild) {\n            console.log('User is in BackstabCO server')\n\n            // Try to get user roles in BackstabCO server\n            // Note: This requires bot token, so we'll simulate for now\n            userRoles = ['Player'] // Default role\n\n            // Check if user has admin permissions in the guild\n            if (backstabGuild.permissions && (parseInt(backstabGuild.permissions) & 0x8)) {\n              userRoles.push('Admin')\n            }\n          }\n        }\n      } catch (guildsError) {\n        console.error('Failed to fetch guilds:', guildsError.message)\n      }\n\n      return NextResponse.json({\n        id: userData.id,\n        username: userData.username,\n        email: userData.email,\n        avatar: userData.avatar,\n        discriminator: userData.discriminator,\n        guilds: userGuilds.length,\n        roles: userRoles,\n        isBackstabMember: userGuilds.some(guild => guild.id === '1366161562238451853'),\n        isMock: false\n      })\n\n    } catch (userError) {\n      console.error('Failed to fetch user data:', userError.message)\n\n      // Return mock data as fallback\n      return NextResponse.json({\n        id: '123456789',\n        username: 'MockUser_' + Date.now().toString().slice(-4),\n        email: '<EMAIL>',\n        avatar: null,\n        isMock: true\n      })\n    }\n\n  } catch (error) {\n    console.error('Discord auth error:', error)\n\n    // Return mock data as fallback\n    return NextResponse.json({\n      id: '123456789',\n      username: 'MockUser_' + Date.now().toString().slice(-4),\n      email: '<EMAIL>',\n      avatar: null,\n      isMock: true\n    })\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEnC,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmB,GAAG;gBAAE,QAAQ;YAAI;QACxE;QAEA,QAAQ,GAAG,CAAC,uCAAuC,KAAK,SAAS,CAAC,GAAG,MAAM;QAE3E,6DAA6D;QAC7D,IAAI,YAAY;QAChB,IAAI,WAAW;QACf,MAAM,cAAc;QAEpB,MAAO,WAAW,eAAe,CAAC,UAAW;YAC3C,IAAI;gBACF;gBACA,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,SAAS,CAAC,EAAE,aAAa;gBAEvE,MAAM,gBAAgB,MAAM,MAAM,wCAAwC;oBACxE,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,IAAI,gBAAgB;wBACxB,WAAW,QAAQ,GAAG,CAAC,iBAAiB;wBACxC,eAAe,QAAQ,GAAG,CAAC,qBAAqB;wBAChD,YAAY;wBACZ,MAAM;wBACN,cAAc;oBAChB;oBACA,QAAQ,YAAY,OAAO,CAAC,OAAO,oBAAoB;gBACzD;gBAEA,IAAI,CAAC,cAAc,EAAE,EAAE;oBACrB,MAAM,YAAY,MAAM,cAAc,IAAI;oBAC1C,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,cAAc,MAAM,CAAC,EAAE,CAAC,EAAE;oBAEjE,IAAI,aAAa,aAAa;wBAC5B,uDAAuD;wBACvD,QAAQ,GAAG,CAAC;wBACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;4BACvB,IAAI;4BACJ,UAAU,cAAc,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;4BACrD,OAAO;4BACP,QAAQ;4BACR,QAAQ;wBACV;oBACF;oBACA;gBACF;gBAEA,YAAY,MAAM,cAAc,IAAI;gBACpC,QAAQ,GAAG,CAAC;gBACZ;YAEF,EAAE,OAAO,YAAY;gBACnB,QAAQ,KAAK,CAAC,CAAC,oBAAoB,EAAE,SAAS,QAAQ,CAAC,EAAE,WAAW,OAAO;gBAE3E,IAAI,aAAa,aAAa;oBAC5B,uDAAuD;oBACvD,QAAQ,GAAG,CAAC;oBACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;wBACvB,IAAI;wBACJ,UAAU,cAAc,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;wBACrD,OAAO;wBACP,QAAQ;wBACR,QAAQ;oBACV;gBACF;gBAEA,oBAAoB;gBACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,OAAO;YAC1D;QACF;QAEA,IAAI,CAAC,WAAW,cAAc;YAC5B,QAAQ,GAAG,CAAC;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,IAAI;gBACJ,UAAU,cAAc,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;gBACrD,OAAO;gBACP,QAAQ;gBACR,QAAQ;YACV;QACF;QAEA,6BAA6B;QAC7B,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,eAAe,MAAM,MAAM,qCAAqC;gBACpE,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,UAAU,YAAY,EAAE;gBACnD;gBACA,QAAQ,YAAY,OAAO,CAAC;YAC9B;YAEA,IAAI,CAAC,aAAa,EAAE,EAAE;gBACpB,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,aAAa,MAAM,EAAE;YAC7D;YAEA,MAAM,WAAW,MAAM,aAAa,IAAI;YACxC,QAAQ,GAAG,CAAC,uBAAuB,SAAS,QAAQ;YAEpD,mDAAmD;YACnD,IAAI,aAAa,EAAE;YACnB,IAAI,YAAY,EAAE;YAElB,IAAI;gBACF,QAAQ,GAAG,CAAC;gBACZ,MAAM,iBAAiB,MAAM,MAAM,4CAA4C;oBAC7E,SAAS;wBACP,eAAe,CAAC,OAAO,EAAE,UAAU,YAAY,EAAE;oBACnD;oBACA,QAAQ,YAAY,OAAO,CAAC;gBAC9B;gBAEA,IAAI,eAAe,EAAE,EAAE;oBACrB,aAAa,MAAM,eAAe,IAAI;oBACtC,QAAQ,GAAG,CAAC,wBAAwB,WAAW,MAAM;oBAErD,wCAAwC;oBACxC,MAAM,gBAAgB,WAAW,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;oBAC5D,IAAI,eAAe;wBACjB,QAAQ,GAAG,CAAC;wBAEZ,6CAA6C;wBAC7C,2DAA2D;wBAC3D,YAAY;4BAAC;yBAAS,CAAC,eAAe;;wBAEtC,mDAAmD;wBACnD,IAAI,cAAc,WAAW,IAAK,SAAS,cAAc,WAAW,IAAI,KAAM;4BAC5E,UAAU,IAAI,CAAC;wBACjB;oBACF;gBACF;YACF,EAAE,OAAO,aAAa;gBACpB,QAAQ,KAAK,CAAC,2BAA2B,YAAY,OAAO;YAC9D;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,IAAI,SAAS,EAAE;gBACf,UAAU,SAAS,QAAQ;gBAC3B,OAAO,SAAS,KAAK;gBACrB,QAAQ,SAAS,MAAM;gBACvB,eAAe,SAAS,aAAa;gBACrC,QAAQ,WAAW,MAAM;gBACzB,OAAO;gBACP,kBAAkB,WAAW,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;gBACxD,QAAQ;YACV;QAEF,EAAE,OAAO,WAAW;YAClB,QAAQ,KAAK,CAAC,8BAA8B,UAAU,OAAO;YAE7D,+BAA+B;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,IAAI;gBACJ,UAAU,cAAc,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;gBACrD,OAAO;gBACP,QAAQ;gBACR,QAAQ;YACV;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QAErC,+BAA+B;QAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,IAAI;YACJ,UAAU,cAAc,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;YACrD,OAAO;YACP,QAAQ;YACR,QAAQ;QACV;IACF;AACF", "debugId": null}}]}