{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/AI/AugmentCode/BackstabCO/backstab-events/src/app/api/discord-auth/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { code } = await request.json()\n    \n    if (!code) {\n      return NextResponse.json({ error: 'No code provided' }, { status: 400 })\n    }\n\n    // For now, return a mock user to test the flow\n    // In production, you would exchange the code for a token here\n    const mockUser = {\n      id: '123456789',\n      username: 'TestUser',\n      email: '<EMAIL>',\n      avatar: null\n    }\n\n    return NextResponse.json(mockUser)\n  } catch (error) {\n    console.error('Discord auth error:', error)\n    return NextResponse.json({ error: 'Authentication failed' }, { status: 500 })\n  }\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEnC,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmB,GAAG;gBAAE,QAAQ;YAAI;QACxE;QAEA,+CAA+C;QAC/C,8DAA8D;QAC9D,MAAM,WAAW;YACf,IAAI;YACJ,UAAU;YACV,OAAO;YACP,QAAQ;QACV;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}