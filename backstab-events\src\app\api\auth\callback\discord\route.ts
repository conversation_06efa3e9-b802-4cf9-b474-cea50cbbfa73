﻿import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const code = searchParams.get('code')

  console.log('Discord callback received, code:', code ? 'present' : 'missing')

  if (!code) {
    console.log('No code provided')
    return NextResponse.redirect('http://localhost:3000?error=no_code')
  }

  try {
    console.log('Attempting to exchange code for token...')
    
    // Exchange code for access token with longer timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 30000) // 30 second timeout

    const tokenResponse = await fetch('https://discord.com/api/oauth2/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: process.env.DISCORD_CLIENT_ID!,
        client_secret: process.env.DISCORD_CLIENT_SECRET!,
        grant_type: 'authorization_code',
        code: code,
        redirect_uri: 'http://localhost:3000/api/auth/callback/discord',
      }),
      signal: controller.signal
    })

    clearTimeout(timeoutId)

    console.log('Token response status:', tokenResponse.status)

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text()
      console.log('Token response error:', errorText)
      return NextResponse.redirect('http://localhost:3000?error=token_request_failed')
    }

    const tokenData = await tokenResponse.json()
    console.log('Token data received:', tokenData.access_token ? 'access_token present' : 'no access_token')

    if (!tokenData.access_token) {
      console.log('No access token in response')
      return NextResponse.redirect('http://localhost:3000?error=no_access_token')
    }

    // Get user info
    console.log('Fetching user info...')
    const userResponse = await fetch('https://discord.com/api/users/@me', {
      headers: {
        Authorization: `Bearer ${tokenData.access_token}`,
      },
    })

    console.log('User response status:', userResponse.status)

    if (!userResponse.ok) {
      const errorText = await userResponse.text()
      console.log('User response error:', errorText)
      return NextResponse.redirect('http://localhost:3000?error=user_fetch_failed')
    }

    const userData = await userResponse.json()
    console.log('User data received:', userData.username || userData.id)

    // Store user data in session/cookie
    const response = NextResponse.redirect('http://localhost:3000?success=true')
    response.cookies.set('discord_user', JSON.stringify(userData), {
      httpOnly: true,
      secure: false,
      maxAge: 60 * 60 * 24 * 7, // 1 week
    })

    console.log('Auth successful, redirecting...')
    return response
  } catch (error) {
    console.error('Auth error details:', error)
    return NextResponse.redirect(`http://localhost:3000?error=auth_failed&details=${encodeURIComponent(error.message)}`)
  }
}
