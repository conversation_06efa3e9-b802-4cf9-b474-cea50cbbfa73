﻿import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const action = searchParams.get('action')

  if (action === 'signin') {
    // Discord OAuth URL - Discord'daki redirect URI ile eşleşecek şekilde
    const discordAuthUrl = `https://discord.com/api/oauth2/authorize?client_id=${process.env.DISCORD_CLIENT_ID}&redirect_uri=${encodeURIComponent('http://localhost:3000/api/auth/callback/discord')}&response_type=code&scope=identify%20email%20guilds%20guilds.members.read`
    
    return NextResponse.redirect(discordAuthUrl)
  }

  if (action === 'session') {
    // Return session info
    return NextResponse.json({ user: null })
  }

  return NextResponse.json({ message: 'Auth API' })
}

export async function POST(request: NextRequest) {
  return NextResponse.json({ message: 'Auth POST' })
}
