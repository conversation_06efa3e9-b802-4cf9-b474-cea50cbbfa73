﻿import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { code } = await request.json()
    
    if (!code) {
      return NextResponse.json({ error: 'No code provided' }, { status: 400 })
    }

    // For now, return a mock user to test the flow
    // In production, you would exchange the code for a token here
    const mockUser = {
      id: '123456789',
      username: 'TestUser',
      email: '<EMAIL>',
      avatar: null
    }

    return NextResponse.json(mockUser)
  } catch (error) {
    console.error('Discord auth error:', error)
    return NextResponse.json({ error: 'Authentication failed' }, { status: 500 })
  }
}
