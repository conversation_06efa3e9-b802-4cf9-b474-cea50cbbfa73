﻿import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Test Discord API connection
    const response = await fetch('https://discord.com/api/v10/applications/@me', {
      headers: {
        'Authorization': `Bot ${process.env.DISCORD_CLIENT_SECRET}`, // This will fail but test connection
      },
    });
    
    return NextResponse.json({ 
      status: 'Discord API reachable',
      statusCode: response.status 
    });
  } catch (error) {
    return NextResponse.json({ 
      status: 'Discord API connection failed',
      error: error.message 
    });
  }
}
