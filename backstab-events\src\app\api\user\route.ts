﻿import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const discordUser = request.cookies.get('discord_user')?.value;
    
    if (!discordUser) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const userData = JSON.parse(discordUser);
    return NextResponse.json(userData);
  } catch (error) {
    return NextResponse.json({ error: 'Invalid session' }, { status: 401 });
  }
}
