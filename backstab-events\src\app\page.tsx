﻿"use client";

import { useEffect, useState } from 'react';

export default function Home() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check URL params for auth status
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const error = urlParams.get('error');

    if (code) {
      // Handle Discord OAuth code on client side
      handleDiscordCallback(code);
    } else if (error) {
      console.error('Auth error:', error);
      setLoading(false);
    } else {
      // Check if user is already logged in
      const savedUser = localStorage.getItem('discord_user');
      if (savedUser) {
        try {
          setUser(JSON.parse(savedUser));
        } catch (e) {
          localStorage.removeItem('discord_user');
        }
      }
      setLoading(false);
    }
  }, []);

  const handleDiscordCallback = async (code) => {
    try {
      setLoading(true);
      
      // Exchange code for token using a CORS proxy or direct approach
      const response = await fetch('/api/discord-auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code }),
      });

      if (response.ok) {
        const userData = await response.json();
        setUser(userData);
        localStorage.setItem('discord_user', JSON.stringify(userData));
        
        // Clean URL
        window.history.replaceState({}, document.title, '/');
      } else {
        throw new Error('Failed to authenticate');
      }
    } catch (error) {
      console.error('Auth failed:', error);
      alert('Discord girişi başarısız oldu. Lütfen tekrar deneyin.');
    }
    setLoading(false);
  };

  const handleDiscordLogin = () => {
    const clientId = '1378540108843712593';
    const redirectUri = encodeURIComponent('http://localhost:3000');
    const scope = encodeURIComponent('identify email guilds guilds.members.read');
    
    const discordAuthUrl = `https://discord.com/api/oauth2/authorize?client_id=${clientId}&redirect_uri=${redirectUri}&response_type=code&scope=${scope}`;
    
    window.location.href = discordAuthUrl;
  };

  const handleLogout = () => {
    setUser(null);
    localStorage.removeItem('discord_user');
    window.location.reload();
  };

  if (loading) {
    return (
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)',
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '1.5rem'
      }}>
        Yükleniyor...
      </div>
    );
  }

  if (user) {
    return (
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)',
        color: 'white',
        fontFamily: 'Arial, sans-serif',
        margin: 0,
        padding: 0,
        width: '100vw'
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          padding: '4rem 1rem',
          textAlign: 'center'
        }}>
          <h1 style={{
            fontSize: '3rem',
            fontWeight: 'bold',
            marginBottom: '2rem',
            background: 'linear-gradient(45deg, #9333ea, #ec4899)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text'
          }}>
            Hoş Geldin, {user.username}!
          </h1>
          
          <div style={{
            background: 'rgba(30, 41, 59, 0.5)',
            border: '1px solid rgba(71, 85, 105, 0.5)',
            borderRadius: '0.75rem',
            padding: '2rem',
            marginBottom: '2rem'
          }}>
            <p style={{ color: '#cbd5e1', marginBottom: '1rem' }}>
              Discord ID: {user.id}
            </p>
            <p style={{ color: '#cbd5e1', marginBottom: '1rem' }}>
              Email: {user.email}
            </p>
            <p style={{ color: '#cbd5e1', marginBottom: '1rem' }}>
              Avatar: {user.avatar ? '' : ''}
            </p>
          </div>

          <button 
            onClick={handleLogout}
            style={{
              background: 'linear-gradient(45deg, #ef4444, #dc2626)',
              color: 'white',
              padding: '1rem 2rem',
              fontSize: '1.125rem',
              border: 'none',
              borderRadius: '0.5rem',
              cursor: 'pointer',
              boxShadow: '0 10px 25px rgba(239, 68, 68, 0.3)',
              transition: 'all 0.3s ease'
            }}
          >
             Çıkış Yap
          </button>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)',
      color: 'white',
      fontFamily: 'Arial, sans-serif',
      margin: 0,
      padding: 0,
      width: '100vw',
      overflow: 'hidden'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '4rem 1rem'
      }}>
        {/* Header */}
        <div style={{ textAlign: 'center', marginBottom: '4rem' }}>
          <h1 style={{
            fontSize: '4rem',
            fontWeight: 'bold',
            marginBottom: '1rem',
            background: 'linear-gradient(45deg, #9333ea, #ec4899)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text'
          }}>
            BackstabCO
          </h1>
          <p style={{
            fontSize: '1.25rem',
            color: '#cbd5e1',
            marginBottom: '2rem'
          }}>
            Albion Online Guild Etkinlik Yönetim Sistemi
          </p>
          <button 
            onClick={handleDiscordLogin}
            style={{
              background: 'linear-gradient(45deg, #9333ea, #ec4899)',
              color: 'white',
              padding: '1rem 2rem',
              fontSize: '1.125rem',
              border: 'none',
              borderRadius: '0.5rem',
              cursor: 'pointer',
              boxShadow: '0 10px 25px rgba(147, 51, 234, 0.3)',
              transition: 'all 0.3s ease'
            }}
          >
             Discord ile Giriş Yap
          </button>
        </div>

        {/* Features Grid */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '1.5rem',
          marginBottom: '4rem'
        }}>
          {[
            { icon: '', title: 'Etkinlik Planlama', desc: 'Guild etkinliklerini kolayca planlayın ve organize edin', color: '#9333ea' },
            { icon: '', title: 'Rol Yönetimi', desc: 'Etkinlikler için gerekli rolleri belirleyin ve katılımcıları yönetin', color: '#3b82f6' },
            { icon: '', title: 'Discord Entegrasyonu', desc: 'Discord rolleri ile otomatik yetkilendirme sistemi', color: '#10b981' },
            { icon: '', title: 'Albion Optimized', desc: 'Albion Online için özel olarak tasarlanmış özellikler', color: '#ef4444' }
          ].map((feature, index) => (
            <div key={index} style={{
              background: 'rgba(30, 41, 59, 0.5)',
              border: '1px solid rgba(71, 85, 105, 0.5)',
              borderRadius: '0.75rem',
              padding: '1.5rem',
              textAlign: 'center',
              transition: 'all 0.3s ease',
              cursor: 'pointer'
            }}>
              <div style={{
                fontSize: '3rem',
                marginBottom: '1rem',
                filter: `drop-shadow(0 0 10px ${feature.color})`
              }}>
                {feature.icon}
              </div>
              <h3 style={{
                fontSize: '1.25rem',
                fontWeight: 'bold',
                marginBottom: '0.5rem',
                color: feature.color
              }}>
                {feature.title}
              </h3>
              <p style={{
                color: '#cbd5e1',
                fontSize: '0.875rem',
                lineHeight: '1.5'
              }}>
                {feature.desc}
              </p>
            </div>
          ))}
        </div>

        {/* How it works */}
        <div style={{
          background: 'rgba(30, 41, 59, 0.3)',
          border: '1px solid rgba(71, 85, 105, 0.5)',
          borderRadius: '0.75rem',
          padding: '2rem',
          maxWidth: '600px',
          margin: '0 auto'
        }}>
          <h2 style={{
            fontSize: '1.875rem',
            fontWeight: 'bold',
            textAlign: 'center',
            marginBottom: '1.5rem',
            color: 'white'
          }}>
             Nasıl Çalışır?
          </h2>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            {[
              'Discord hesabınızla giriş yapın',
              'BackstabCO sunucusundaki rolünüz otomatik kontrol edilir',
              'Etkinlikleri görüntüleyin ve katılım sağlayın',
              'Admin/Moderatör iseniz yeni etkinlikler oluşturun'
            ].map((step, index) => (
              <div key={index} style={{
                display: 'flex',
                alignItems: 'flex-start',
                gap: '0.75rem'
              }}>
                <div style={{
                  width: '2rem',
                  height: '2rem',
                  background: 'linear-gradient(45deg, #9333ea, #ec4899)',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontWeight: 'bold',
                  fontSize: '0.875rem',
                  flexShrink: 0
                }}>
                  {index + 1}
                </div>
                <p style={{
                  color: '#cbd5e1',
                  margin: 0,
                  lineHeight: '1.6'
                }}>
                  {step}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
