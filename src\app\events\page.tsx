"use client";

import { useEffect, useState } from 'react';
import Link from 'next/link';

export default function EventsPage() {
  const [user, setUser] = useState(null);
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if user is logged in
    const savedUser = localStorage.getItem('discord_user');
    if (savedUser) {
      try {
        setUser(JSON.parse(savedUser));
      } catch (e) {
        localStorage.removeItem('discord_user');
      }
    }

    // Load events (mock data for now)
    loadEvents();
  }, []);

  const loadEvents = async () => {
    // Mock events data - will be replaced with real API call
    const mockEvents = [
      {
        id: 1,
        title: 'ZvZ Training',
        description: 'Büyük grup savaşı antrenmanı. Tüm guild üyeleri davetli.',
        date: '2025-06-03T19:00:00Z',
        type: 'Training',
        requiredRoles: ['Player'],
        maxParticipants: 50,
        currentParticipants: 23,
        status: 'upcoming',
        organizer: 'Admin',
        location: 'Caerleon'
      },
      {
        id: 2,
        title: 'Dungeon Run - Avalon',
        description: 'Avalon dungeon grubu. T8+ gear gerekli.',
        date: '2025-06-02T20:30:00Z',
        type: 'PvE',
        requiredRoles: ['Player'],
        maxParticipants: 5,
        currentParticipants: 5,
        status: 'full',
        organizer: 'Moderator',
        location: 'Avalon Roads'
      },
      {
        id: 3,
        title: 'Guild Meeting',
        description: 'Haftalık guild toplantısı. Önemli duyurular var.',
        date: '2025-06-01T18:00:00Z',
        type: 'Meeting',
        requiredRoles: ['Player'],
        maxParticipants: 100,
        currentParticipants: 45,
        status: 'completed',
        organizer: 'Admin',
        location: 'Discord Voice'
      },
      {
        id: 4,
        title: 'Faction Warfare',
        description: 'Faction savaşına katılım. PvP deneyimi öneriliyor.',
        date: '2025-06-04T21:00:00Z',
        type: 'PvP',
        requiredRoles: ['Player'],
        maxParticipants: 20,
        currentParticipants: 12,
        status: 'upcoming',
        organizer: 'Moderator',
        location: 'Royal Continent'
      }
    ];

    setEvents(mockEvents);
    setLoading(false);
  };

  const getEventTypeColor = (type) => {
    switch (type) {
      case 'Training': return '#9333ea';
      case 'PvE': return '#10b981';
      case 'PvP': return '#ef4444';
      case 'Meeting': return '#3b82f6';
      default: return '#6b7280';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'upcoming': return '#10b981';
      case 'full': return '#f59e0b';
      case 'completed': return '#6b7280';
      default: return '#6b7280';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'upcoming': return 'Yaklaşan';
      case 'full': return 'Dolu';
      case 'completed': return 'Tamamlandı';
      default: return 'Bilinmiyor';
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)',
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '1.5rem'
      }}>
        Etkinlikler yükleniyor...
      </div>
    );
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)',
      color: 'white',
      fontFamily: 'Arial, sans-serif',
      padding: '2rem 1rem'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '2rem'
        }}>
          <div>
            <h1 style={{
              fontSize: '2.5rem',
              fontWeight: 'bold',
              marginBottom: '0.5rem',
              background: 'linear-gradient(45deg, #9333ea, #ec4899)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text'
            }}>
              📅 Guild Etkinlikleri
            </h1>
            <p style={{ color: '#cbd5e1', fontSize: '1.1rem' }}>
              {user ? `Hoş geldin, ${user.username}! Mevcut etkinlikleri görüntüle ve katıl.` : 'Mevcut guild etkinliklerini görüntüleyin. Katılmak için giriş yapın.'}
            </p>
          </div>
          
          <div style={{ display: 'flex', gap: '1rem' }}>
            <Link href="/" style={{ textDecoration: 'none' }}>
              <button style={{
                background: 'linear-gradient(45deg, #6b7280, #4b5563)',
                color: 'white',
                padding: '0.75rem 1.5rem',
                fontSize: '1rem',
                border: 'none',
                borderRadius: '0.5rem',
                cursor: 'pointer'
              }}>
                🏠 Ana Sayfa
              </button>
            </Link>
            
            {user?.roles?.includes('Admin') || user?.roles?.includes('Moderator') ? (
              <Link href="/events/create" style={{ textDecoration: 'none' }}>
                <button style={{
                  background: 'linear-gradient(45deg, #9333ea, #ec4899)',
                  color: 'white',
                  padding: '0.75rem 1.5rem',
                  fontSize: '1rem',
                  border: 'none',
                  borderRadius: '0.5rem',
                  cursor: 'pointer'
                }}>
                  ➕ Etkinlik Oluştur
                </button>
              </Link>
            ) : null}
          </div>
        </div>

        {/* Events Grid */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
          gap: '1.5rem'
        }}>
          {events.map((event) => (
            <div key={event.id} style={{
              background: 'rgba(30, 41, 59, 0.5)',
              border: '1px solid rgba(71, 85, 105, 0.5)',
              borderRadius: '0.75rem',
              padding: '1.5rem',
              transition: 'all 0.3s ease',
              cursor: 'pointer'
            }}>
              {/* Event Header */}
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'flex-start',
                marginBottom: '1rem'
              }}>
                <div>
                  <h3 style={{
                    fontSize: '1.25rem',
                    fontWeight: 'bold',
                    marginBottom: '0.5rem',
                    color: 'white'
                  }}>
                    {event.title}
                  </h3>
                  <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
                    <span style={{
                      background: getEventTypeColor(event.type),
                      color: 'white',
                      padding: '0.25rem 0.75rem',
                      borderRadius: '1rem',
                      fontSize: '0.75rem',
                      fontWeight: 'bold'
                    }}>
                      {event.type}
                    </span>
                    <span style={{
                      background: getStatusColor(event.status),
                      color: 'white',
                      padding: '0.25rem 0.75rem',
                      borderRadius: '1rem',
                      fontSize: '0.75rem',
                      fontWeight: 'bold'
                    }}>
                      {getStatusText(event.status)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Event Details */}
              <p style={{
                color: '#cbd5e1',
                marginBottom: '1rem',
                lineHeight: '1.5'
              }}>
                {event.description}
              </p>

              {/* Event Info */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: '1fr 1fr',
                gap: '0.5rem',
                marginBottom: '1rem',
                fontSize: '0.875rem'
              }}>
                <div style={{ color: '#cbd5e1' }}>
                  <strong>📅 Tarih:</strong><br />
                  {formatDate(event.date)}
                </div>
                <div style={{ color: '#cbd5e1' }}>
                  <strong>📍 Konum:</strong><br />
                  {event.location}
                </div>
                <div style={{ color: '#cbd5e1' }}>
                  <strong>👥 Katılım:</strong><br />
                  {event.currentParticipants}/{event.maxParticipants}
                </div>
                <div style={{ color: '#cbd5e1' }}>
                  <strong>👤 Organizatör:</strong><br />
                  {event.organizer}
                </div>
              </div>

              {/* Action Button */}
              <button style={{
                width: '100%',
                background: event.status === 'full' 
                  ? 'linear-gradient(45deg, #6b7280, #4b5563)'
                  : event.status === 'completed'
                  ? 'linear-gradient(45deg, #6b7280, #4b5563)'
                  : 'linear-gradient(45deg, #10b981, #059669)',
                color: 'white',
                padding: '0.75rem',
                fontSize: '1rem',
                border: 'none',
                borderRadius: '0.5rem',
                cursor: event.status === 'upcoming' ? 'pointer' : 'not-allowed',
                fontWeight: 'bold'
              }}>
                {event.status === 'full' ? '❌ Dolu' :
                 event.status === 'completed' ? '✅ Tamamlandı' :
                 '🎯 Katıl'}
              </button>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {events.length === 0 && (
          <div style={{
            textAlign: 'center',
            padding: '4rem 2rem',
            color: '#cbd5e1'
          }}>
            <h3 style={{ fontSize: '1.5rem', marginBottom: '1rem' }}>
              Henüz etkinlik bulunmuyor
            </h3>
            <p>Yeni etkinlikler eklendiğinde burada görünecek.</p>
          </div>
        )}
      </div>
    </div>
  );
}
