"use client";

import { useEffect, useState } from 'react';

export default function Home() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check URL params for auth status
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const error = urlParams.get('error');

    if (code) {
      // Handle Discord OAuth code on client side
      handleDiscordCallback(code);
    } else if (error) {
      console.error('Auth error:', error);
      setLoading(false);
    } else {
      // Check if user is already logged in
      const savedUser = localStorage.getItem('discord_user');
      if (savedUser) {
        try {
          setUser(JSON.parse(savedUser));
        } catch (e) {
          localStorage.removeItem('discord_user');
        }
      }
      setLoading(false);
    }
  }, []);

  const handleDiscordCallback = async (code) => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/discord-auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code }),
      });

      if (response.ok) {
        const userData = await response.json();
        setUser(userData);
        localStorage.setItem('discord_user', JSON.stringify(userData));
        
        // Clean URL
        window.history.replaceState({}, document.title, '/');
      } else {
        throw new Error('Failed to authenticate');
      }
    } catch (error) {
      console.error('Auth failed:', error);
      alert('Discord girişi başarısız oldu. Lütfen tekrar deneyin.');
    }
    setLoading(false);
  };

  const handleDiscordLogin = () => {
    const clientId = '1378540108843712593';
    const redirectUri = encodeURIComponent('http://localhost:3000');
    const scope = encodeURIComponent('identify email guilds guilds.members.read');
    
    const discordAuthUrl = `https://discord.com/api/oauth2/authorize?client_id=${clientId}&redirect_uri=${redirectUri}&response_type=code&scope=${scope}`;
    
    window.location.href = discordAuthUrl;
  };

  const handleLogout = () => {
    setUser(null);
    localStorage.removeItem('discord_user');
    window.location.reload();
  };

  if (loading) {
    return (
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)',
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '1.5rem'
      }}>
        Yükleniyor...
      </div>
    );
  }

  if (user) {
    return (
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)',
        color: 'white',
        fontFamily: 'Arial, sans-serif',
        margin: 0,
        padding: 0,
        width: '100vw'
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          padding: '4rem 1rem',
          textAlign: 'center'
        }}>
          <h1 style={{
            fontSize: '3rem',
            fontWeight: 'bold',
            marginBottom: '2rem',
            background: 'linear-gradient(45deg, #9333ea, #ec4899)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text'
          }}>
            Hoş Geldin, {user.username}!
            {user.isMock && <span style={{ fontSize: '1rem', color: '#fbbf24' }}> (Test Modu)</span>}
          </h1>
          
          {/* User Info Card */}
          <div style={{
            background: 'rgba(30, 41, 59, 0.5)',
            border: '1px solid rgba(71, 85, 105, 0.5)',
            borderRadius: '0.75rem',
            padding: '2rem',
            marginBottom: '2rem',
            textAlign: 'left'
          }}>
            <h3 style={{ 
              color: '#9333ea', 
              marginBottom: '1.5rem',
              textAlign: 'center',
              fontSize: '1.5rem'
            }}>
              👤 Kullanıcı Bilgileri
            </h3>
            
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
              <div>
                <p style={{ color: '#cbd5e1', marginBottom: '0.5rem' }}>
                  <strong>Discord ID:</strong> {user.id}
                </p>
                <p style={{ color: '#cbd5e1', marginBottom: '0.5rem' }}>
                  <strong>Email:</strong> {user.email || 'Gizli'}
                </p>
                <p style={{ color: '#cbd5e1', marginBottom: '0.5rem' }}>
                  <strong>Avatar:</strong> {user.avatar ? '✅ Var' : '❌ Yok'}
                </p>
              </div>
              <div>
                <p style={{ color: '#cbd5e1', marginBottom: '0.5rem' }}>
                  <strong>Sunucu Sayısı:</strong> {user.guilds || 'Bilinmiyor'}
                </p>
                <p style={{ color: '#cbd5e1', marginBottom: '0.5rem' }}>
                  <strong>BackstabCO Üyesi:</strong> {user.isBackstabMember ? '✅ Evet' : '❌ Hayır'}
                </p>
                <p style={{ color: '#cbd5e1', marginBottom: '0.5rem' }}>
                  <strong>Roller:</strong> {user.roles ? user.roles.join(', ') : 'Bilinmiyor'}
                </p>
              </div>
            </div>
          </div>

          {/* Role-based Actions */}
          {user.roles && user.roles.includes('Admin') && (
            <div style={{
              background: 'rgba(147, 51, 234, 0.1)',
              border: '1px solid rgba(147, 51, 234, 0.3)',
              borderRadius: '0.75rem',
              padding: '1.5rem',
              marginBottom: '2rem'
            }}>
              <h3 style={{ color: '#9333ea', marginBottom: '1rem' }}>
                👑 Admin Paneli
              </h3>
              <p style={{ color: '#cbd5e1', marginBottom: '1rem' }}>
                Admin yetkileriniz bulunuyor. Etkinlik oluşturabilir ve yönetebilirsiniz.
              </p>
              <button style={{
                background: 'linear-gradient(45deg, #9333ea, #ec4899)',
                color: 'white',
                padding: '0.75rem 1.5rem',
                fontSize: '1rem',
                border: 'none',
                borderRadius: '0.5rem',
                cursor: 'pointer',
                marginRight: '1rem'
              }}>
                📅 Etkinlik Oluştur
              </button>
              <button style={{
                background: 'linear-gradient(45deg, #3b82f6, #1d4ed8)',
                color: 'white',
                padding: '0.75rem 1.5rem',
                fontSize: '1rem',
                border: 'none',
                borderRadius: '0.5rem',
                cursor: 'pointer'
              }}>
                ⚙️ Ayarlar
              </button>
            </div>
          )}

          {user.roles && user.roles.includes('Moderator') && !user.roles.includes('Admin') && (
            <div style={{
              background: 'rgba(59, 130, 246, 0.1)',
              border: '1px solid rgba(59, 130, 246, 0.3)',
              borderRadius: '0.75rem',
              padding: '1.5rem',
              marginBottom: '2rem'
            }}>
              <h3 style={{ color: '#3b82f6', marginBottom: '1rem' }}>
                🛡️ Moderatör Paneli
              </h3>
              <p style={{ color: '#cbd5e1', marginBottom: '1rem' }}>
                Moderatör yetkileriniz bulunuyor. Etkinlikleri yönetebilirsiniz.
              </p>
              <button style={{
                background: 'linear-gradient(45deg, #3b82f6, #1d4ed8)',
                color: 'white',
                padding: '0.75rem 1.5rem',
                fontSize: '1rem',
                border: 'none',
                borderRadius: '0.5rem',
                cursor: 'pointer'
              }}>
                📋 Etkinlikleri Yönet
              </button>
            </div>
          )}

          {/* Regular User Actions */}
          <div style={{
            background: 'rgba(16, 185, 129, 0.1)',
            border: '1px solid rgba(16, 185, 129, 0.3)',
            borderRadius: '0.75rem',
            padding: '1.5rem',
            marginBottom: '2rem'
          }}>
            <h3 style={{ color: '#10b981', marginBottom: '1rem' }}>
              🎮 Etkinlikler
            </h3>
            <p style={{ color: '#cbd5e1', marginBottom: '1rem' }}>
              Mevcut etkinlikleri görüntüleyin ve katılım sağlayın.
            </p>
            <button
              onClick={() => window.location.href = '/events'}
              style={{
              background: 'linear-gradient(45deg, #10b981, #059669)',
              color: 'white',
              padding: '0.75rem 1.5rem',
              fontSize: '1rem',
              border: 'none',
              borderRadius: '0.5rem',
              cursor: 'pointer',
              marginRight: '1rem'
            }}>
              📅 Etkinlikleri Görüntüle
            </button>
            <button style={{
              background: 'linear-gradient(45deg, #f59e0b, #d97706)',
              color: 'white',
              padding: '0.75rem 1.5rem',
              fontSize: '1rem',
              border: 'none',
              borderRadius: '0.5rem',
              cursor: 'pointer'
            }}>
              📊 Katılım Geçmişim
            </button>
          </div>

          <button 
            onClick={handleLogout}
            style={{
              background: 'linear-gradient(45deg, #ef4444, #dc2626)',
              color: 'white',
              padding: '1rem 2rem',
              fontSize: '1.125rem',
              border: 'none',
              borderRadius: '0.5rem',
              cursor: 'pointer',
              boxShadow: '0 10px 25px rgba(239, 68, 68, 0.3)',
              transition: 'all 0.3s ease'
            }}
          >
            🚪 Çıkış Yap
          </button>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)',
      color: 'white',
      fontFamily: 'Arial, sans-serif',
      margin: 0,
      padding: 0,
      width: '100vw',
      overflow: 'hidden'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '4rem 1rem'
      }}>
        {/* Header */}
        <div style={{ textAlign: 'center', marginBottom: '4rem' }}>
          <h1 style={{
            fontSize: '4rem',
            fontWeight: 'bold',
            marginBottom: '1rem',
            background: 'linear-gradient(45deg, #9333ea, #ec4899)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text'
          }}>
            BackstabCO
          </h1>
          <p style={{
            fontSize: '1.25rem',
            color: '#cbd5e1',
            marginBottom: '2rem'
          }}>
            Albion Online Guild Etkinlik Yönetim Sistemi
          </p>
          <button 
            onClick={handleDiscordLogin}
            style={{
              background: 'linear-gradient(45deg, #9333ea, #ec4899)',
              color: 'white',
              padding: '1rem 2rem',
              fontSize: '1.125rem',
              border: 'none',
              borderRadius: '0.5rem',
              cursor: 'pointer',
              boxShadow: '0 10px 25px rgba(147, 51, 234, 0.3)',
              transition: 'all 0.3s ease'
            }}
          >
            🎮 Discord ile Giriş Yap
          </button>
        </div>
      </div>
    </div>
  );
}
